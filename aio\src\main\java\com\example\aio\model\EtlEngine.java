package com.example.aio.model;

import lombok.Data;

/**
 * @Date 2022/2/16 22:29
 * @Created yanggld
 * @Description
 */
@Data
public class EtlEngine {
    private long id;
    private String appCode;
    private String collectCode;
    private String schemaName;
    private String schemaNameDynamic;
    private int sinkMode = 0;
    private String etlJson;
    private String sinkType;
    private String sinkName;
    private String sinkNameDynamic;
    private String sinkPk;
    private String sinkFieldsType;
    private String sinkFieldsJson;
    private String sinkStruct;
    private String sinkExtend;
    private String sinkTableTtl;
    private String sinkTableTtlField;
    private String sinkTableTtlUnit;
    private boolean sinkEnable;
    private boolean sinkLandEnable;
    private String latestType;
    private boolean latestTable;
    private String latestTableName;
    private String latestTablePk;
    private String latestFieldsType;
    private int latestTableTtl;
    private int basePriority;
    private boolean updateSchema;
    private boolean showInDataLog;

    /**
     * schema是否独立创建，获取依赖上传数据创建
     */
    private boolean sinkSchemaCreatedSelf = true;
    /**
     * sink是否独立创建，获取依赖上传数据创建
     */
    private boolean sinkNameCreatedSelf = true;
    /**
     * sink的ddl
     */
    private String sinkDdl;
    /**
     * 修改的操作
     */
    private String changeOp;
    /**
     * 存储设定Id
     */
    private Long storageSettingId;

    public EtlEngine() {
    }

    public EtlEngine(String appCode, String collectCode, String schemaName, String etlJson,
                     String sinkType, String sinkName, String sinkPk, int basePriority) {
        this.appCode = appCode;
        this.collectCode = collectCode;
        this.schemaName = schemaName;
        this.etlJson = etlJson;
        this.sinkType = sinkType;
        this.sinkName = sinkName;
        this.sinkPk = sinkPk;
        this.basePriority = basePriority;
    }
}
