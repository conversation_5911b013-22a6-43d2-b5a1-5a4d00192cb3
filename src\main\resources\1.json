[{"fieldCode": "deviceId", "fieldType": "VARCHAR", "valuePath": "BasicInfo.deviceId"}, {"fieldCode": "eid", "fieldType": "VARCHAR", "valuePath": "BasicInfo.eid"}, {"fieldCode": "aiopsItem", "fieldType": "VARCHAR", "valuePath": "BasicInfo.aiopsItem"}, {"fieldCode": "aiId", "fieldType": "VARCHAR", "valuePath": "BasicInfo.aiId"}, {"fieldCode": "deviceCollectDetailId", "fieldType": "VARCHAR", "valuePath": "BasicInfo.deviceCollectDetailId"}, {"fieldCode": "flumeTimestamp", "fieldType": "VARCHAR", "valuePath": "BasicInfo.flumeTimestamp"}, {"fieldCode": "uploadDataModelCode", "fieldType": "VARCHAR", "valuePath": "BasicInfo.uploadDataModelCode"}, {"fieldCode": "collectConfigId", "fieldType": "VARCHAR", "valuePath": "BasicInfo.collectConfigId"}, {"fieldCode": "collectedTime", "fieldType": "DATETIME", "valuePath": "BasicInfo.collectedTime"}, {"fieldCode": "CompleteOnTimeRate", "fieldType": "DECIMAL", "valuePath": "DataContent.CompleteOnTimeRate"}]