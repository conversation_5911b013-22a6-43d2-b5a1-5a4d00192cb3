package com.example.aio.controller;

import com.alibaba.fastjson2.JSONObject;
import com.digiwin.dap.middleware.commons.util.EncryptUtils;
import com.digiwin.dap.middleware.domain.DapEncrypt;
import com.example.aio.model.BatchProcessResult;
import com.example.aio.model.CsvUserData;
import com.example.aio.model.Mapping;
import com.example.aio.model.NoticeModuleRequest;
import com.example.aio.model.ResultModel;
import com.example.aio.model.Role;
import com.example.aio.model.User;
import com.google.gson.Gson;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.client.RestTemplate;
import org.apache.http.impl.client.CloseableHttpClient;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/iam")
public class IamController {


    @Value("${esc.integration.iamAddress}")
    private String iamAddress;

    private RestTemplate restTemplate;
    private String key = "2T4cWkN3sQavj2sARE4xWrTLaF4kXsSSCJE1deASCc/VN7sRp61WaN3b7vkFw9ZC";

    public IamController() {
        this.restTemplate = new RestTemplate();
    }
    @GetMapping(value = "/roleGet")
    public Object roleGet() {
//        String token = RequestUtil.getHeaderToken();
//        String userId = RequestUtil.getHeaderUserId();
//        TestController.RoleDelete req = new TestController.RoleDelete();
//        req.setSid(userSid);

        HttpHeaders headers = new HttpHeaders();
        DapEncrypt dapEncrypt = EncryptUtils.encryptSign(key, getResultModel());
        String encrypt = JSONObject.toJSONString(dapEncrypt);
        String signHeader = dapEncrypt.getSignHeader();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("digi-middleware-auth-app", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE1MTUxMzM3ODgwNjEsInNpZCI6OTQ5MTY2MDE1NTYzMDQyODE2LCJpZCI6IlNlcnZpY2VDbG91ZCJ9.9xL7lSrjTvv_hRYaU5RXVMIZ9Zkz_PbA5oXxyUYekmI");
        headers.set("digi-middleware-sign-arg", signHeader);
//        headers.set("digi-middleware-auth-user", token);
//        encrypt.setSignHeader(null);
        HttpEntity<String> request = new HttpEntity<>(encrypt, headers);
        Object o = restTemplate.postForObject(iamAddress+"/api/iam/integrate/import/escloud/tenant", request, Object.class);

        return o;
    }

    private List<ResultModel> getResultModel(){
        ResultModel resultModel = new ResultModel();
        User user = new User();
        user.setId("<EMAIL>");
        user.setName("陈超");
        user.setEmail("<EMAIL>");
        user.setTenantId("00990004");
        user.setTenantName("00990004");

        List<Mapping> mappingList = new ArrayList<>();
        Mapping mapping = new Mapping();
        mapping.setTenantId("00990004");
        mapping.setProviderId("escloud");
        mapping.setVerifyUserId("5137e0ac2f924beaa6f7d72cd96a1975");
        mappingList.add(mapping);
        List<ResultModel> resultModels = new ArrayList<>();
        Role role = new Role();
        role.setId("endUser");
        role.setName("一般用户");
        List<Role> roles = new ArrayList<>();
        roles.add(role);
        resultModel.setRoleInfo(roles);
        resultModel.setMappingInfo(mappingList);
        resultModel.setUser(user);
        resultModels.add(resultModel);
        return resultModels;
    }

    @GetMapping(value = "/roleGetV2")
    public Object roleGetV2() {

//        HttpPost httpPost = new HttpPost(iamAddress+"/api/iam/integrate/import/escloud/tenant");

        List<ResultModel> resultModel = getResultModel();
        Pair<String, String> reqPair = getReq(resultModel);
        try (CloseableHttpClient httpClient = HttpClientBuilder.create()
                .build()) {
            HttpUriRequest requestBuilder = RequestBuilder.post().setUri(iamAddress + "/api/iam/integrate/import/escloud/tenant")
                    .setEntity(new StringEntity(reqPair.getRight(), StandardCharsets.UTF_8))
                    .setHeader(HttpHeaders.CONTENT_TYPE, "application/json")
                    .setHeader("digi-middleware-auth-app", "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE1MTUxMzM3ODgwNjEsInNpZCI6OTQ5MTY2MDE1NTYzMDQyODE2LCJpZCI6IlNlcnZpY2VDbG91ZCJ9.9xL7lSrjTvv_hRYaU5RXVMIZ9Zkz_PbA5oXxyUYekmI")
                    .setHeader("digi-middleware-sign-arg", reqPair.getLeft()).build();
            CloseableHttpResponse response = httpClient.execute(requestBuilder);
            String msg = response.getStatusLine().getStatusCode() + " " + EntityUtils.toString(response.getEntity());
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.OK.value()) {
                return "ERROR";
            }
            return msg;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
    private static final Gson gson = new Gson();

    public static Gson getGson(){
        return gson;
    }
    private Pair<String,String> getReq(Object reqObj){
        DapEncrypt encrypt = EncryptUtils.encryptSign(key, reqObj);
        String signHeader = encrypt.getSignHeader();
        encrypt.setSignHeader(null);
        String reqString = JSONObject.toJSONString(encrypt);
        String reqStringG = getGson().toJson(encrypt);
        return Pair.of(signHeader, reqString);
    }

    /**
     * 批量处理CSV文件中的用户数据，调用通知模块保存API
     * @param file CSV文件
     * @param targetIp 目标服务器IP地址
     * @return 批量处理结果
     */
    @PostMapping(value = "/batchNoticeModule")
    public BatchProcessResult batchNoticeModule(@RequestParam("file") MultipartFile file,
                                               @RequestParam("targetIp") String targetIp,
                                                @RequestParam("token") String token) {
        List<CsvUserData> csvDataList = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();
        List<CsvUserData> failedRecords = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        try {
            // 解析CSV文件
            csvDataList = parseCsvFile(file);

            // 循环调用通知模块API
            for (CsvUserData csvData : csvDataList) {
                try {
                    boolean success = callNoticeModuleApi(csvData, targetIp,token);
                    if (success) {
                        successCount++;
                    } else {
                        failureCount++;
                        failedRecords.add(csvData);
                        errorMessages.add("调用API失败: " + csvData.toString());
                    }
                } catch (Exception e) {
                    failureCount++;
                    failedRecords.add(csvData);
                    errorMessages.add("处理记录异常: " + csvData.toString() + ", 错误: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            errorMessages.add("解析CSV文件失败: " + e.getMessage());
            failureCount = csvDataList.size();
            failedRecords.addAll(csvDataList);
        }

        return new BatchProcessResult(csvDataList.size(), successCount, failureCount, errorMessages, failedRecords);
    }

    /**
     * 解析CSV文件
     * @param file CSV文件
     * @return 解析后的用户数据列表
     * @throws IOException
     */
    private List<CsvUserData> parseCsvFile(MultipartFile file) throws IOException {
        List<CsvUserData> csvDataList = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 跳过空行
                if (line.trim().isEmpty()) {
                    continue;
                }

                // 按逗号分割CSV行
                String[] fields = line.split(",");
                if (fields.length >= 4) {
                    try {
                        Long authUserSid = Long.parseLong(fields[0].trim());
                        String authUserId = fields[1].trim();
                        String email = fields[2].trim();
                        Long eid = Long.parseLong(fields[3].trim());

                        CsvUserData csvData = new CsvUserData(authUserSid, authUserId, email, eid);
                        csvDataList.add(csvData);
                    } catch (NumberFormatException e) {
                        throw new RuntimeException("CSV数据格式错误，行内容: " + line + ", 错误: " + e.getMessage());
                    }
                } else {
                    throw new RuntimeException("CSV数据格式错误，列数不足，行内容: " + line);
                }
            }
        }

        return csvDataList;
    }

    /**
     * 调用通知模块保存API
     * @param csvData CSV用户数据
     * @param targetIp 目标服务器IP
     * @return 是否成功
     */
    private boolean callNoticeModuleApi(CsvUserData csvData, String targetIp,String token) {
        try {
            // 构建请求数据，固定参数按照要求设置
            NoticeModuleRequest request = new NoticeModuleRequest(
                1,                          // nmId 固定为1
                true,                       // enabled 固定为true
                "1",                        // nwIds 固定为"1"
                "1,2,3,4",                 // nrlIds 固定为"1,2,3,4"
                csvData.getEmail(),         // email 从CSV获取
                csvData.getAuthUserId(),    // authUserId 从CSV获取
                csvData.getAuthUserSid(),   // authUserSid 从CSV获取
                csvData.getEid()            // eid 从CSV获取
            );

            // 构建请求URL
            String url = "http://" + targetIp + "/tenant/notice/module/save";

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("token",token);

            // 创建请求实体
            String requestBody = JSONObject.toJSONString(request);
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送POST请求
            Object response = restTemplate.postForObject(url, requestEntity, Object.class);

            // 这里可以根据实际API的响应格式来判断是否成功
            // 暂时认为没有抛异常就是成功
            return true;

        } catch (Exception e) {
            System.err.println("调用通知模块API失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 测试方法：解析示例CSV数据
     * @return 解析结果
     */
    @GetMapping(value = "/testCsvParse")
    public List<CsvUserData> testCsvParse() {
        List<CsvUserData> result = new ArrayList<>();

        // 模拟CSV数据
        String[] csvLines = {
            "41338904412736,<EMAIL>,<EMAIL>,99990000",
            "41338982142528,<EMAIL>,<EMAIL>,99990000"
        };

        for (String line : csvLines) {
            String[] fields = line.split(",");
            if (fields.length >= 4) {
                try {
                    Long authUserSid = Long.parseLong(fields[0].trim());
                    String authUserId = fields[1].trim();
                    String email = fields[2].trim();
                    Long eid = Long.parseLong(fields[3].trim());

                    CsvUserData csvData = new CsvUserData(authUserSid, authUserId, email, eid);
                    result.add(csvData);
                } catch (NumberFormatException e) {
                    System.err.println("解析错误: " + line + ", 错误: " + e.getMessage());
                }
            }
        }

        return result;
    }

    /**
     * 测试方法：生成请求JSON格式
     * @return 请求JSON示例
     */
    @GetMapping(value = "/testRequestFormat")
    public NoticeModuleRequest testRequestFormat() {
        // 创建示例请求对象
        NoticeModuleRequest request = new NoticeModuleRequest(
            1,                                    // nmId 固定为1
            true,                                // enabled 固定为true (boolean类型)
            "1",                                 // nwIds 固定为"1"
            "1,2,3,4",                          // nrlIds 固定为"1,2,3,4"
            "<EMAIL>",               // email 示例
            "<EMAIL>",               // authUserId 示例
            41338904412736L,                    // authUserSid 示例
            99990000L                           // eid 示例
        );

        return request;
    }


}
