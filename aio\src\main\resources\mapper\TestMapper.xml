<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.aio.dao.TestMapper">


    <select id="selectByMenu" resultType="com.example.aio.model.DataExaminationMenu">
        select * from data_examination_menu where menuCode = #{menuCode}
    </select>
    <select id="selectByMenuByParentId" resultType="com.example.aio.model.DataExaminationMenu">
        select * from data_examination_menu where parentId = #{parentId}
    </select>
    <insert id="insertMenu" parameterType="com.example.aio.model.DataExaminationMenu">
        INSERT INTO data_examination_menu (id, menuName,menuName_CN,menuName_TW,menuCode, parentId, menuStatus,menuType,menuSource,imgUrl,appCode,eid,sid, menuOrder)
        VALUES (#{id}, #{menuName}, #{menuName_CN}, #{menuName_TW},#{menuCode}, #{parentId}, #{menuStatus}, #{menuType}, #{menuSource}, #{imgUrl}, #{appCode}, #{eid}, #{sid}, #{menuOrder})
    </insert>

    <select id="selectEtlFieldType" resultType="com.example.aio.model.EtlEngine">
        SELECT t.*
        FROM `dcp-db`.etl_engine t
        WHERE collectCode=#{collectCode} and sinkType = 'starrocks'
    </select>
</mapper>