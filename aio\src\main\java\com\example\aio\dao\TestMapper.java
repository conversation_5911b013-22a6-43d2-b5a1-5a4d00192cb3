package com.example.aio.dao;

import com.example.aio.model.DataExaminationMenu;
import com.example.aio.model.EtlEngine;

import java.util.List;

public interface TestMapper  {
    List<DataExaminationMenu> selectByMenu(String menuCode);
    List<DataExaminationMenu> selectByMenuByParentId(String parentId);
    void insertMenu(DataExaminationMenu menu);
    EtlEngine selectEtlFieldType(String collectCode);
}