package com.example.aio.model;

import java.util.List;

/**
 * 批量处理结果
 */
public class BatchProcessResult {
    private int totalCount;
    private int successCount;
    private int failureCount;
    private List<String> errorMessages;
    private List<CsvUserData> failedRecords;

    public BatchProcessResult() {
    }

    public BatchProcessResult(int totalCount, int successCount, int failureCount, 
                             List<String> errorMessages, List<CsvUserData> failedRecords) {
        this.totalCount = totalCount;
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.errorMessages = errorMessages;
        this.failedRecords = failedRecords;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public int getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(int successCount) {
        this.successCount = successCount;
    }

    public int getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(int failureCount) {
        this.failureCount = failureCount;
    }

    public List<String> getErrorMessages() {
        return errorMessages;
    }

    public void setErrorMessages(List<String> errorMessages) {
        this.errorMessages = errorMessages;
    }

    public List<CsvUserData> getFailedRecords() {
        return failedRecords;
    }

    public void setFailedRecords(List<CsvUserData> failedRecords) {
        this.failedRecords = failedRecords;
    }

    @Override
    public String toString() {
        return "BatchProcessResult{" +
                "totalCount=" + totalCount +
                ", successCount=" + successCount +
                ", failureCount=" + failureCount +
                ", errorMessages=" + errorMessages +
                ", failedRecords=" + failedRecords +
                '}';
    }
}
