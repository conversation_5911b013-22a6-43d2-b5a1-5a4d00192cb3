### 解决方案：使用另一个广受好评的自动化脚本

别担心，这是一个小问题。我们将使用另一个非常流行且持续在维护的WireGuard安装脚本（来自Nyr），它的使用方法几乎完全一样。

请在您的VPS上执行以下更新后的命令：

**Step 1: 下载新的安装脚本**

这个命令会强制使用IPv4下载（避免`No route to host`的延迟），并从新的有效地址下载脚本。

```bash
wget -4 https://git.io/wireguard -O wireguard-install.sh
```

**Step 2: 赋予脚本执行权限**

```bash
chmod +x wireguard-install.sh
```

**Step 3: 运行脚本**

```bash
sudo ./wireguard-install.sh
```

**接下来会发生什么？**

这个脚本和之前的类似，也会向您提问来完成自动配置：
1.  它会询问您的公网IP地址，通常会自动检测，您直接按**回车**确认即可。
2.  可能会询问公网网卡，同样直接**回车**用默认的。
3.  可能会询问WireGuard监听的端口，可以直接**回车**用默认的 `51820`，或者输入一个您自己的。
4.  它会要求您为第一个客户端输入一个**名字 (Name)**，例如 `my-pc`，然后按**回车**。
5.  之后脚本会自动完成所有安装和配置。

**Step 4: 获取客户端配置**

安装完成后，脚本会提示它生成了一个配置文件，路径通常是 `/root/wg0-client-my-pc.conf`（文件名取决于您输入的名字）。

您可以通过以下方式获取配置：
*   **显示二维码 (用于手机)**: 脚本可能会提供一个选项直接显示二维码，您用手机App扫描即可。
*   **显示配置文件内容 (用于电脑)**:
    ```bash
    cat /root/wg0-client-my-pc.conf
    ```
    复制终端里输出的全部内容，然后在您电脑的WireGuard客户端里导入或粘贴即可。