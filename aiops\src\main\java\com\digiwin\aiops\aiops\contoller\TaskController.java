package com.digiwin.aiops.aiops.contoller;

import com.alibaba.fastjson.JSONObject;
import com.digiwin.aiops.aiops.model.AsyncBatchProcessResult;
import com.digiwin.aiops.aiops.model.BatchProcessResult;
import com.digiwin.aiops.aiops.model.CsvUserData;
import com.digiwin.aiops.aiops.model.NoticeModuleRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/iam")
public class TaskController {

    @Resource
    private RestTemplate restTemplate;
    private static final Logger log = LoggerFactory.getLogger(TaskController.class);


    /**
     * 批量处理CSV文件中的用户数据，调用通知模块保存API（异步版本）
     *
     * @param file     CSV文件
     * @param targetIp 目标服务器IP地址
     * @param token    认证令牌
     * @return 异步批量处理结果
     */
    @PostMapping(value = "/batchNoticeModule")
    public AsyncBatchProcessResult batchNoticeModule(@RequestParam("file") MultipartFile file,
                                                     @RequestParam("targetIp") String targetIp,
                                                     @RequestParam("token") String token) {
        // 生成任务ID
        String taskId = UUID.randomUUID().toString();

        try {
            // 解析CSV文件获取总数
            List<CsvUserData> csvDataList = parseCsvFile(file);

            // 异步处理批量任务
            processBatchNoticeModuleAsync(taskId, csvDataList, targetIp, token);

            return new AsyncBatchProcessResult(taskId, "PROCESSING",
                    "批量处理任务已启动，任务ID: " + taskId, csvDataList.size());

        } catch (Exception e) {
            log.error("启动批量处理任务失败: " + e.getMessage(), e);
            return new AsyncBatchProcessResult(taskId, "FAILED",
                    "启动批量处理任务失败: " + e.getMessage(), 0);
        }
    }

    /**
     * 批量处理CSV文件中的用户数据，调用通知模块保存API（同步版本，保留原有功能）
     *
     * @param file     CSV文件
     * @param targetIp 目标服务器IP地址
     * @param token    认证令牌
     * @return 批量处理结果
     */
    @PostMapping(value = "/batchNoticeModuleSync")
    public BatchProcessResult batchNoticeModuleSync(@RequestParam("file") MultipartFile file,
                                                    @RequestParam("targetIp") String targetIp,
                                                    @RequestParam("token") String token) {
        List<CsvUserData> csvDataList = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();
        List<CsvUserData> failedRecords = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        try {
            // 解析CSV文件
            csvDataList = parseCsvFile(file);

            // 循环调用通知模块API
            for (CsvUserData csvData : csvDataList) {
                try {
                    boolean success = callNoticeModuleApi(csvData, targetIp, token);
                    if (success) {
                        successCount++;
                    } else {
                        failureCount++;
                        failedRecords.add(csvData);
                        errorMessages.add("调用API失败: " + csvData.toString());
                    }
                    log.info("处理结果: " + csvData.toString() + ", 处理结果: " + (success ? "成功" : "失败"));
                    TimeUnit.MILLISECONDS.sleep(500);
                } catch (Exception e) {
                    failureCount++;
                    failedRecords.add(csvData);
                    errorMessages.add("处理记录异常: " + csvData.toString() + ", 错误: " + e.getMessage());
                }

            }
        } catch (Exception e) {
            errorMessages.add("解析CSV文件失败: " + e.getMessage());
            failureCount = csvDataList.size();
            failedRecords.addAll(csvDataList);
        }

        return new BatchProcessResult(csvDataList.size(), successCount, failureCount, errorMessages, failedRecords);
    }

    /**
     * 解析CSV文件
     *
     * @param file CSV文件
     * @return 解析后的用户数据列表
     * @throws IOException
     */
    private List<CsvUserData> parseCsvFile(MultipartFile file) throws IOException {
        List<CsvUserData> csvDataList = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 跳过空行
                if (line.trim().isEmpty()) {
                    continue;
                }

                // 按逗号分割CSV行
                String[] fields = line.split(",");
                if (fields.length >= 4) {
                    try {
                        Long authUserSid = Long.parseLong(fields[0].trim());
                        String authUserId = fields[1].trim();
                        String email = fields[2].trim();
                        Long eid = Long.parseLong(fields[3].trim());

                        CsvUserData csvData = new CsvUserData(authUserSid, authUserId, email, eid);
                        csvDataList.add(csvData);
                    } catch (NumberFormatException e) {
                        throw new RuntimeException("CSV数据格式错误，行内容: " + line + ", 错误: " + e.getMessage());
                    }
                } else {
                    throw new RuntimeException("CSV数据格式错误，列数不足，行内容: " + line);
                }
            }
        }

        return csvDataList;
    }

    /**
     * 调用通知模块保存API
     *
     * @param csvData  CSV用户数据
     * @param targetIp 目标服务器IP
     * @return 是否成功
     */
    private boolean callNoticeModuleApi(CsvUserData csvData, String targetIp, String token) {
        try {
            // 构建请求数据，固定参数按照要求设置
            NoticeModuleRequest request = new NoticeModuleRequest(
                    1,                          // nmId 固定为1
                    true,                       // enabled 固定为true
                    "1",                        // nwIds 固定为"1"
                    "1,2,3,4",                 // nrlIds 固定为"1,2,3,4"
                    csvData.getEmail(),         // email 从CSV获取
                    csvData.getAuthUserId(),    // authUserId 从CSV获取
                    csvData.getAuthUserSid(),   // authUserSid 从CSV获取
                    csvData.getEid()            // eid 从CSV获取
            );

            // 构建请求URL
            String url = "http://" + targetIp + "/tenant/notice/module/save";

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("token", token);

            // 创建请求实体
            String requestBody = JSONObject.toJSONString(request);
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            // 发送POST请求
            Object response = restTemplate.postForObject(url, requestEntity, Object.class);
            if (Objects.isNull(response)) {
                return false;
            }
            LinkedHashMap response1 = (LinkedHashMap) response;
            // 这里可以根据实际API的响应格式来判断是否成功
            // 暂时认为没有抛异常就是成功
            return "0".equals(response1.get("code").toString());

        } catch (Exception e) {
            System.err.println("调用通知模块API失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 异步处理批量通知模块任务
     *
     * @param taskId      任务ID
     * @param csvDataList CSV数据列表
     * @param targetIp    目标服务器IP
     * @param token       认证令牌
     */
    @Async("batchProcessExecutor")
    public CompletableFuture<Void> processBatchNoticeModuleAsync(String taskId,
                                                                 List<CsvUserData> csvDataList,
                                                                 String targetIp,
                                                                 String token) {
        log.info("开始异步处理批量任务，任务ID: {}, 总数: {}", taskId, csvDataList.size());

        List<String> errorMessages = new ArrayList<>();
        List<CsvUserData> failedRecords = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        try {
            // 循环调用通知模块API
            for (int i = 0; i < csvDataList.size(); i++) {
                CsvUserData csvData = csvDataList.get(i);
                try {
                    boolean success = callNoticeModuleApi(csvData, targetIp, token);
                    if (success) {
                        successCount++;
                        log.info("任务ID: {}, 进度: {}/{}, 处理成功: {}",
                                taskId, i + 1, csvDataList.size(), csvData.toString());
                    } else {
                        failureCount++;
                        failedRecords.add(csvData);
                        errorMessages.add("调用API失败: " + csvData.toString());
                        log.warn("任务ID: {}, 进度: {}/{}, 处理失败: {}",
                                taskId, i + 1, csvDataList.size(), csvData.toString());
                    }

                    // 添加延迟以避免过于频繁的请求
                    TimeUnit.MILLISECONDS.sleep(500);

                } catch (Exception e) {
                    failureCount++;
                    failedRecords.add(csvData);
                    errorMessages.add("处理记录异常: " + csvData.toString() + ", 错误: " + e.getMessage());
                    log.error("任务ID: {}, 进度: {}/{}, 处理异常: {}, 错误: {}",
                            taskId, i + 1, csvDataList.size(), csvData.toString(), e.getMessage());
                }
            }

            log.info("异步批量处理任务完成，任务ID: {}, 总数: {}, 成功: {}, 失败: {}",
                    taskId, csvDataList.size(), successCount, failureCount);

        } catch (Exception e) {
            log.error("异步批量处理任务执行异常，任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 测试方法：解析示例CSV数据
     *
     * @return 解析结果
     */
    @GetMapping(value = "/testCsvParse")
    public List<CsvUserData> testCsvParse() {
        List<CsvUserData> result = new ArrayList<>();

        // 模拟CSV数据
        String[] csvLines = {
                "41338904412736,<EMAIL>,<EMAIL>,99990000",
                "41338982142528,<EMAIL>,<EMAIL>,99990000"
        };

        for (String line : csvLines) {
            String[] fields = line.split(",");
            if (fields.length >= 4) {
                try {
                    Long authUserSid = Long.parseLong(fields[0].trim());
                    String authUserId = fields[1].trim();
                    String email = fields[2].trim();
                    Long eid = Long.parseLong(fields[3].trim());

                    CsvUserData csvData = new CsvUserData(authUserSid, authUserId, email, eid);
                    result.add(csvData);
                } catch (NumberFormatException e) {
                    System.err.println("解析错误: " + line + ", 错误: " + e.getMessage());
                }
            }
        }

        return result;
    }

    /**
     * 测试方法：生成请求JSON格式
     *
     * @return 请求JSON示例
     */
    @GetMapping(value = "/testRequestFormat")
    public NoticeModuleRequest testRequestFormat() {
        // 创建示例请求对象
        NoticeModuleRequest request = new NoticeModuleRequest(
                1,                                    // nmId 固定为1
                true,                                // enabled 固定为true (boolean类型)
                "1",                                 // nwIds 固定为"1"
                "1,2,3,4",                          // nrlIds 固定为"1,2,3,4"
                "<EMAIL>",               // email 示例
                "<EMAIL>",               // authUserId 示例
                41338904412736L,                    // authUserSid 示例
                99990000L                           // eid 示例
        );

        return request;
    }
}
