package com.example.aio.model;

/**
 * CSV用户数据模型
 */
public class CsvUserData {
    private Long authUserSid;
    private String authUserId;
    private String email;
    private Long eid;

    public CsvUserData() {
    }

    public CsvUserData(Long authUserSid, String authUserId, String email, Long eid) {
        this.authUserSid = authUserSid;
        this.authUserId = authUserId;
        this.email = email;
        this.eid = eid;
    }

    public Long getAuthUserSid() {
        return authUserSid;
    }

    public void setAuthUserSid(Long authUserSid) {
        this.authUserSid = authUserSid;
    }

    public String getAuthUserId() {
        return authUserId;
    }

    public void setAuthUserId(String authUserId) {
        this.authUserId = authUserId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Long getEid() {
        return eid;
    }

    public void setEid(Long eid) {
        this.eid = eid;
    }

    @Override
    public String toString() {
        return "CsvUserData{" +
                "authUserSid=" + authUserSid +
                ", authUserId='" + authUserId + '\'' +
                ", email='" + email + '\'' +
                ", eid=" + eid +
                '}';
    }
}
