package com.digiwin.aiops.aiops.model;

import lombok.Data;

/**
 * 通知模块保存请求DTO
 */

public class NoticeModuleRequest {
    private Integer nmId;
    private Boolean specialEnabled;
    private String nwIds;
    private String nrlIds;
    private String email;
    private String authUserId;
    private Long authUserSid;
    private Long eid;

    public NoticeModuleRequest() {
    }

    public NoticeModuleRequest(Integer nmId, Boolean specialEnabled, String nwIds, String nrlIds,
                               String email, String authUserId, Long authUserSid, Long eid) {
        this.nmId = nmId;
        this.specialEnabled = specialEnabled;
        this.nwIds = nwIds;
        this.nrlIds = nrlIds;
        this.email = email;
        this.authUserId = authUserId;
        this.authUserSid = authUserSid;
        this.eid = eid;
    }

    public Integer getNmId() {
        return nmId;
    }

    public void setNmId(Integer nmId) {
        this.nmId = nmId;
    }

    public Boolean getSpecialEnabled() {
        return specialEnabled;
    }

    public void setSpecialEnabled(Boolean specialEnabled) {
        this.specialEnabled = specialEnabled;
    }

    public String getNwIds() {
        return nwIds;
    }

    public void setNwIds(String nwIds) {
        this.nwIds = nwIds;
    }

    public String getNrlIds() {
        return nrlIds;
    }

    public void setNrlIds(String nrlIds) {
        this.nrlIds = nrlIds;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAuthUserId() {
        return authUserId;
    }

    public void setAuthUserId(String authUserId) {
        this.authUserId = authUserId;
    }

    public Long getAuthUserSid() {
        return authUserSid;
    }

    public void setAuthUserSid(Long authUserSid) {
        this.authUserSid = authUserSid;
    }

    public Long getEid() {
        return eid;
    }

    public void setEid(Long eid) {
        this.eid = eid;
    }
}
