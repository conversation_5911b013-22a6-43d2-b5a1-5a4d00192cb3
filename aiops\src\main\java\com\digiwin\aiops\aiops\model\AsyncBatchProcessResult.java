package com.digiwin.aiops.aiops.model;

/**
 * 异步批量处理结果
 */
public class AsyncBatchProcessResult {
    private String taskId;
    private String status;
    private String message;
    private int totalCount;
    private long startTime;

    public AsyncBatchProcessResult() {
    }

    public AsyncBatchProcessResult(String taskId, String status, String message, int totalCount) {
        this.taskId = taskId;
        this.status = status;
        this.message = message;
        this.totalCount = totalCount;
        this.startTime = System.currentTimeMillis();
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    @Override
    public String toString() {
        return "AsyncBatchProcessResult{" +
                "taskId='" + taskId + '\'' +
                ", status='" + status + '\'' +
                ", message='" + message + '\'' +
                ", totalCount=" + totalCount +
                ", startTime=" + startTime +
                '}';
    }
}
